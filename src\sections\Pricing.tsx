
import React, { useState, useEffect } from 'react';
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";
import { toast } from "sonner";
import { motion } from "framer-motion";
import PricingCard from '@/components/PricingCard';

interface PricingPlan {
  id: string;
  name_plan: string;
  price: string;
  features: string;
  perks: string | null;
  validity_period?: string;
  unlock_limit?: number;
  readcode_limit?: number;
  other_operations_limit?: string;
  ai_access?: string;
}

interface Offer {
  id: string;
  percentage: string | null;
  expiry_at: string | null;
  status: string | null;
}

interface PricingProps {
  theme?: 'software' | 'hardware';
}

const Pricing: React.FC<PricingProps> = ({ theme = 'software' }) => {
  const { toast: toastNotify } = useToast();
  const [plans, setPlans] = useState<PricingPlan[]>([]);
  const [activeOffer, setActiveOffer] = useState<Offer | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchPricingPlans = async () => {
      try {
        // Fetch pricing plans
        const { data: plansData, error: plansError } = await supabase
          .from('pricing')
          .select('*')
          .order('price::numeric');

        if (plansError) throw plansError;

        // Fetch active offers
        const now = new Date().toISOString();
        const { data: offersData, error: offersError } = await supabase
          .from('offers')
          .select('*')
          .eq('status', 'Plans')
          .gt('expiry_at', now)
          .order('created_at', { ascending: false })
          .limit(1);

        if (offersError) throw offersError;

        // Set the active offer if available
        if (offersData && offersData.length > 0) {
          setActiveOffer(offersData[0]);
        }

        setPlans(plansData || []);
      } catch (error) {
        toastNotify({
          title: "Error",
          description: "Failed to fetch pricing information. Please try again later.",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    fetchPricingPlans();
  }, [toastNotify]);

  // Function to parse features string into an array
  const parseFeatures = (featuresStr: string): string[] => {
    try {
      return featuresStr.split('\n').map(feature => feature.trim()).filter(Boolean);
    } catch (e) {
      return [];
    }
  };

  // Function to parse perks string into an array
  const parsePerks = (perksStr: string | null): string[] => {
    if (!perksStr) return [];
    try {
      return perksStr.split('\n').map(perk => perk.trim()).filter(Boolean);
    } catch (e) {
      return [];
    }
  };

  // Function to calculate discounted price
  const calculateDiscountedPrice = (originalPrice: string): { original: string; discounted: string | null; savings: string | null } => {
    if (!activeOffer || !activeOffer.percentage) {
      return { original: originalPrice, discounted: null, savings: null };
    }

    const price = parseFloat(originalPrice);
    const discountPercentage = parseFloat(activeOffer.percentage.replace('%', ''));

    if (isNaN(price) || isNaN(discountPercentage)) {
      return { original: originalPrice, discounted: null, savings: null };
    }

    const discountAmount = price * (discountPercentage / 100);
    const discountedPrice = price - discountAmount;

    return {
      original: originalPrice,
      discounted: discountedPrice.toFixed(2),
      savings: discountAmount.toFixed(2)
    };
  };

  // Function to format expiry date
  const formatExpiryDate = (expiryDate: string): string => {
    const date = new Date(expiryDate);
    const now = new Date();
    const diffTime = date.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays <= 0) return 'Expired';
    if (diffDays === 1) return 'Expires tomorrow';
    if (diffDays <= 7) return `Expires in ${diffDays} days`;

    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const handleChoosePlan = (plan: PricingPlan) => {
    toast(`You've selected the ${plan.name_plan} plan`, {
      description: "Contact sales for next steps.",
      action: {
        label: "Contact Sales",
        onClick: () => {
          const contactSection = document.getElementById('contact');
          if (contactSection) {
            contactSection.scrollIntoView({ behavior: 'smooth' });
          }
        }
      }
    });
  };



  // Function to determine the plan variant based on theme
  const getPlanVariant = (planName: string): 'plus' | 'pro' | 'max' | 'basic' => {
    const lowerPlanName = planName.toLowerCase();
    if (lowerPlanName.includes('max')) return 'max';
    if (lowerPlanName.includes('standard')) return 'pro';
    if (lowerPlanName.includes('basic')) return theme === 'hardware' ? 'basic' : 'basic';
    return theme === 'hardware' ? 'basic' : 'plus';
  };

  // Function to determine if plan is recommended
  const isRecommended = (planName: string): boolean => {
    const lowerPlanName = planName.toLowerCase();
    return lowerPlanName.includes('recommended') || lowerPlanName.includes('max');
  };



  return (
    
    <div className="py-20 bg-[#111111] text-gray-300 relative overflow-hidden" id="pricing">
      <div className="absolute inset-0 z-1 pointer-events-none" style={{
        background: 'linear-gradient(to bottom, transparent 0%, #111111 90%), radial-gradient(ellipse at center, transparent 40%, #111111 95%)'
      }}></div>

      {/* Header Section */}
      <div className="container mx-auto px-4 relative z-10 mb-16">
        <div className="text-center">
          <div className="mb-6">
            <span className={`bg-[#1a1a1a] border border-gray-700 px-4 py-1 rounded-full text-xs sm:text-sm font-medium ${theme === 'hardware' ? 'text-pegasus-blue-400' : 'text-orange-400'}`}>
             Pricing Plans
            </span>
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-semibold text-white leading-tight mb-4">
            Choose Your <span className={theme === 'hardware' ? 'text-pegasus-blue-400' : 'text-orange-400'}>Perfect Plan</span>
          </h2>
          <p className="text-base sm:text-lg lg:text-xl text-gray-400 max-w-2xl mx-auto">
            Professional pricing plans designed for technicians and repair businesses
          </p>
        </div>
      </div>

      {/* Special Offer Banner */}
      {activeOffer && activeOffer.expiry_at && (
        <motion.div
          className="container mx-auto px-4 mb-8 relative z-10"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="bg-gradient-to-r from-red-500 to-orange-500 text-white rounded-lg p-4 text-center shadow-lg">
            <div className="flex items-center justify-center gap-2 mb-2">
              <span className="text-2xl">🔥</span>
              <h3 className="text-xl font-bold">Special Limited Time Offer!</h3>
              <span className="text-2xl">🔥</span>
            </div>
            <p className="text-lg">
              Save <strong>{activeOffer.percentage}</strong> on all plans!
              <span className="ml-2 text-yellow-200">
                {formatExpiryDate(activeOffer.expiry_at)}
              </span>
            </p>
          </div>
        </motion.div>
      )}

      {/* Pricing Plans */}
      <div className="container mx-auto px-4 relative z-10">
        {loading ? (
          <div className="flex flex-col items-center justify-center min-h-[400px]">
            <Loader2 className={`h-12 w-12 ${theme === 'hardware' ? 'text-pegasus-blue-400' : 'text-orange-400'} animate-spin mb-4`} />
            <p className="text-lg text-gray-400">Loading pricing plans...</p>
          </div>
        ) : plans.length > 0 ? (
            <motion.div
              className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-6xl mx-auto"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6 }}
            >
              {plans.map((plan, index) => {
                const features = parseFeatures(plan.features);
                const perks = parsePerks(plan.perks);
                const planVariant = getPlanVariant(plan.name_plan);
                const recommended = isRecommended(plan.name_plan);

                // Apply discount if offer is active
                const { original, discounted } = calculateDiscountedPrice(plan.price);

                return (
                  <PricingCard
                    key={plan.id}
                    id={plan.id}
                    name={plan.name_plan}
                    price={discounted || original}
                    originalPrice={discounted ? original : undefined}
                    features={features}
                    perks={perks}
                    index={index}
                    recommended={recommended}
                    variant={planVariant}
                    onChoosePlan={() => handleChoosePlan(plan)}
                    unlockLimit={plan.unlock_limit}
                    readcodeLimit={plan.readcode_limit}
                    otherOperationsLimit={plan.other_operations_limit}
                    aiAccess={plan.ai_access}
                    validityPeriod={plan.validity_period}
                  />
                );
              })}
            </motion.div>
          ) : (
            <div className="text-center py-20 px-4 max-w-2xl mx-auto">
              <h3 className="text-2xl font-bold text-white mb-4">No pricing plans available at the moment</h3>
              <p className="text-gray-400 mb-6">
                Please check back later or contact us for custom pricing tailored to your needs.
              </p>
              <Button
                onClick={() => {
                  const contactSection = document.getElementById('contact');
                  if (contactSection) {
                    contactSection.scrollIntoView({ behavior: 'smooth' });
                  }
                }}
                className={theme === 'hardware'
                  ? "bg-pegasus-blue hover:bg-pegasus-blue-700 text-white"
                  : "bg-pegasus-orange hover:bg-pegasus-orange-600 text-white"
                }
              >
                Contact Us
              </Button>
            </div>
          )}
        </div>
      </div>
  );
};

export default Pricing;
