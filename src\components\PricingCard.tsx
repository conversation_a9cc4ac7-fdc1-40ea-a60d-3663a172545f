
import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Check, ChevronRight, Unlock, HardDrive, Cpu, Bot, BarChart3, Gift, Clock } from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { motion } from "framer-motion";
import { cn } from '@/lib/utils';
import { toast } from "sonner";

interface PricingPlanProps {
  id: string;
  name: string;
  price: string;
  originalPrice?: string;
  features: string[];
  perks?: string[];
  index: number;
  recommended?: boolean;
  onChoosePlan: () => void;
  variant?: 'plus' | 'pro' | 'max' | 'basic';
  unlockLimit?: number;
  readcodeLimit?: number;
  otherOperationsLimit?: string;
  aiAccess?: string;
  validityPeriod?: string;
}

const PricingCard: React.FC<PricingPlanProps> = ({
  id,
  name,
  price,
  originalPrice,
  features,
  perks = [],
  index,
  recommended = false,
  onChoosePlan,
  variant = 'plus',
  unlockLimit,
  readcodeLimit,
  otherOperationsLimit,
  aiAccess,
  validityPeriod
}) => {
  // Suppress unused variable warnings for now
  console.log('Features passed:', features);
  const getGradientClass = () => {
    switch (variant) {
      case 'max':
        return 'from-purple-500 to-indigo-600';
      case 'pro':
        return 'from-blue-500 to-cyan-600';
      case 'basic':
        return 'from-pegasus-blue to-pegasus-blue-600';
      default:
        return 'from-orange-500 to-amber-600';
    }
  };

  const getTextColorClass = () => {
    switch (variant) {
      case 'max':
        return 'text-purple-600 dark:text-purple-400';
      case 'pro':
        return 'text-blue-600 dark:text-blue-400';
      case 'basic':
        return 'text-pegasus-blue dark:text-pegasus-blue-400';
      default:
        return 'text-pegasus-orange';
    }
  };



  const getPlanSubtitle = () => {
    switch (name.toLowerCase()) {
      case 'basic':
        return 'Start Small, Stay Smart';
      case 'plus':
        return 'More Power, Same Simplicity';
      case 'pro':
        return 'For the Serious User';
      case 'max':
        return 'Unlock the Power';
      case 'max pro':
        return 'Built Without Limits';
      case 'max pro plus':
        return 'The Ultimate Experience';
      default:
        return 'Perfect for You';
    }
  };

  const getPlanDescription = () => {
    switch (name.toLowerCase()) {
      case 'basic':
        return 'Perfect for beginners exploring the basics with light usage and affordable pricing.';
      case 'plus':
        return 'Ideal for growing users needing deeper insights and expanded capacity.';
      case 'pro':
        return 'Great for professionals who need flexibility and robust features over a longer period.';
      case 'max':
        return 'Built for power users who demand performance and value long-term efficiency.';
      case 'max pro':
        return 'Unlock your full potential with more storage, more operations, and unlimited AI interaction.';
      case 'max pro plus':
        return 'Designed for elite users and organizations who need complete freedom, performance, and premium access.';
      default:
        return 'Choose the plan that fits your needs.';
    }
  };

  const getFeatureIcon = (feature: string) => {
    const lowerFeature = feature.toLowerCase();
    if (lowerFeature.includes('unlock')) return <Unlock className="h-4 w-4" />;
    if (lowerFeature.includes('storage') || lowerFeature.includes('file')) return <HardDrive className="h-4 w-4" />;
    if (lowerFeature.includes('operations') || lowerFeature.includes('other')) return <Cpu className="h-4 w-4" />;
    if (lowerFeature.includes('ai') || lowerFeature.includes('assistant')) return <Bot className="h-4 w-4" />;
    if (lowerFeature.includes('analysis') || lowerFeature.includes('data')) return <BarChart3 className="h-4 w-4" />;
    if (lowerFeature.includes('read') || lowerFeature.includes('code')) return <Check className="h-4 w-4" />;
    if (lowerFeature.includes('free') || lowerFeature.includes('limited')) return <Gift className="h-4 w-4" />;
    return <Check className="h-4 w-4" />;
  };

  const handleContactSales = () => {
    const contactSection = document.getElementById('contact');
    if (contactSection) {
      contactSection.scrollIntoView({ behavior: 'smooth' });
    }
    toast.info(`For more information about our ${name} plan, please contact our sales team.`);
  };

  const containerVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        delay: index * 0.3,
        ease: [0.22, 1, 0.36, 1]
      }
    },
    hover: {
      y: -10,
      boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)",
      transition: { duration: 0.3 }
    },
    exit: {
      opacity: 0,
      y: -50,
      transition: {
        duration: 0.5
      }
    }
  };

  return (
    <motion.div
      className="h-full"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      exit="exit"
      whileHover="hover"
    >
      <Card
        className={cn(
          "h-full flex flex-col overflow-hidden transition-all duration-300 relative transform",
          recommended ? "shadow-xl border-pegasus-orange dark:border-pegasus-orange scale-105 z-10" : "shadow-md border-gray-200 dark:border-gray-700",
        )}
      >
        {recommended && (
          <div className="absolute -right-12 top-7 bg-pegasus-orange text-white py-1 px-10 transform rotate-45 shadow-md text-sm font-semibold z-20">
            Recommended
          </div>
        )}
        <div className={cn(`h-2 w-full bg-gradient-to-r ${getGradientClass()}`)}></div>
        <CardHeader className="pt-6 pb-4">
          <div className="mb-2">
            <CardTitle className={cn("text-2xl font-bold", getTextColorClass())}>
              {name} Plan
            </CardTitle>
            <p className="text-sm text-gray-600 dark:text-gray-400 font-medium">
              {getPlanSubtitle()}
            </p>
          </div>

          <div className="mt-4 flex items-baseline">
            {originalPrice && (
              <span className="text-lg text-gray-400 dark:text-gray-500 font-normal line-through mr-2">${originalPrice}</span>
            )}
            <span className="text-3xl font-bold text-gray-900 dark:text-gray-100">${price}</span>
            <span className="text-sm text-gray-500 dark:text-gray-400 font-normal ml-1">
              {validityPeriod || 'PER MONTH'}
            </span>
          </div>

          {originalPrice && (
            <div className="mt-2 flex items-center gap-2">
              <span className="bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 px-2 py-1 rounded-full text-xs font-semibold">
                SPECIAL OFFER
              </span>
              <Clock className="h-4 w-4 text-red-500" />
              <span className="text-xs text-red-500 font-medium">Limited Time</span>
            </div>
          )}
        </CardHeader>
        <CardContent className="px-6 pb-6 flex-grow">
          <div className="mb-4">
            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">
              What you get:
            </h4>

            {/* Core Features */}
            <div className="space-y-3 mb-6">
              {unlockLimit && (
                <div className="flex items-center gap-3">
                  <div className={`p-1 rounded ${getTextColorClass()}`}>
                    <Unlock className="h-4 w-4" />
                  </div>
                  <span className="text-gray-700 dark:text-gray-300">
                    Unlock Operations: <strong>{unlockLimit}</strong> {validityPeriod?.includes('Month') ? '/ mo' : 'total'}
                  </span>
                </div>
              )}

              {readcodeLimit !== undefined && readcodeLimit > 0 && (
                <div className="flex items-center gap-3">
                  <div className={`p-1 rounded ${getTextColorClass()}`}>
                    <Check className="h-4 w-4" />
                  </div>
                  <span className="text-gray-700 dark:text-gray-300">
                    Read Code: <strong>{readcodeLimit}</strong> total
                  </span>
                </div>
              )}

              {otherOperationsLimit && (
                <div className="flex items-center gap-3">
                  <div className={`p-1 rounded ${getTextColorClass()}`}>
                    <Cpu className="h-4 w-4" />
                  </div>
                  <span className="text-gray-700 dark:text-gray-300">
                    Other Operations: <strong>{otherOperationsLimit}</strong> {validityPeriod?.includes('Month') ? '/ mo' : otherOperationsLimit === 'Unlimited' ? '' : 'total'}
                  </span>
                </div>
              )}

              {aiAccess && (
                <div className="flex items-center gap-3">
                  <div className={`p-1 rounded ${getTextColorClass()}`}>
                    <Bot className="h-4 w-4" />
                  </div>
                  <span className="text-gray-700 dark:text-gray-300">
                    AI Assistant Access: <strong>{aiAccess}</strong> messages
                  </span>
                </div>
              )}
            </div>

            {/* Additional Features from perks */}
            {perks.length > 0 && (
              <div className="space-y-2">
                {perks.map((perk, i) => (
                  <div key={i} className="flex items-center gap-3">
                    <div className={`p-1 rounded ${getTextColorClass()}`}>
                      {getFeatureIcon(perk)}
                    </div>
                    <span className="text-gray-700 dark:text-gray-300">
                      {perk}
                    </span>
                  </div>
                ))}
              </div>
            )}
          </div>

          <div className="mt-6 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
            <p className="text-sm text-gray-600 dark:text-gray-400 italic">
              {getPlanDescription()}
            </p>
          </div>
        </CardContent>
        <CardFooter className="bg-gray-50 dark:bg-gray-800/50 px-6 py-4 border-t border-gray-100 dark:border-gray-800">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                className={cn(
                  "w-full bg-gradient-to-r hover:opacity-90 transition-all duration-300 text-white shadow-md",
                  getGradientClass()
                )}
              >
                Choose Plan <ChevronRight className="ml-1 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem
                onClick={onChoosePlan}
                className="cursor-pointer"
              >
                Purchase Now
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={handleContactSales}
                className="cursor-pointer"
              >
                Contact Sales
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => window.location.href = '/faq'}
                className="cursor-pointer"
              >
                Read FAQ
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </CardFooter>
      </Card>
    </motion.div>
  );
};

export default PricingCard;
